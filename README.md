# MyBinder - Group Chat dengan Note-Taking Terintegrasi

MyBinder adalah aplikasi web full-stack modern yang menggabungkan **group chat** dan **note-taking terintegrasi** dalam satu platform yang powerful. Dibangun dengan teknologi terdepan, aplikasi ini memungkinkan tim untuk berkomunikasi dan berkolaborasi dengan mudah melalui sistem pesan real-time dan catatan berbasis blok yang fleksibel.

## ✨ Fitur Utama

### 💬 Advanced Group Chat
- **Multi-Role System**: Owner → Admin → Member dengan hak akses berbeda
- **Public/Private Groups**: Kontrol visibilitas grup
- **Threading Support**: Reply system dengan nested conversations
- **Real-time Updates**: Polling-based dengan optimistic updates
- **Mobile Optimization**: Optimal untuk semua screen sizes
- **Member Management**: Invite, kick, promote/demote, transfer ownership

### 📝 Block-based Notes System
- **Multiple Block Types**: Text, Headings (H1-H3), Lists, Code, Quotes
- **Collaborative Editing**: Multiple users dapat edit bersamaan
- **Order Management**: Drag & drop dengan automatic reordering
- **Version Control**: Track changes dan authors
- **Group Organization**: Notes per grup dengan search functionality

### 🔐 Enterprise-Grade Security
- **JWT Authentication**: Secure token-based dengan HTTP-only cookies
- **Role-based Authorization**: Granular permissions per feature
- **Input Validation**: Comprehensive validation dengan Zod schemas
- **Password Security**: bcrypt hashing dengan salt rounds 12
- **XSS Prevention**: React built-in + custom sanitization

### 📱 Mobile-First Experience
- **Responsive Design**: Optimal untuk semua screen sizes
- **Mobile Navigation**: Collapsible sidebar dengan smart positioning

## 🛠️ Stack Teknologi Modern

### **Core Technologies**
- **Frontend**: Next.js 15.5.3 + React 19.1.0 + TypeScript 5.x
- **Styling**: Tailwind CSS 4.x dengan mobile-first approach
- **Backend**: Next.js API Routes dengan serverless architecture
- **Database**: PostgreSQL + Prisma ORM 6.16.2
- **Authentication**: JWT + bcryptjs dengan HTTP-only cookies
- **Validation**: Zod untuk runtime type validation
- **Real-time**: Polling-based (Socket.IO ready untuk future)

### **Development & Testing**
- **Testing**: Jest + React Testing Library + Playwright E2E
- **Code Quality**: ESLint + Prettier + TypeScript strict mode
- **Development**: Docker Compose untuk local database
- **Deployment**: Vercel (Frontend) + Supabase (Database)

### **Key Features**
- ✅ **100% TypeScript**: End-to-end type safety
- ✅ **Mobile-First**: Responsive design dengan touch gestures
- ✅ **Production Ready**: Deployed dan fully functional
- ✅ **Comprehensive Testing**: Unit + Integration + E2E tests

## 📋 Prasyarat

Sebelum menjalankan aplikasi, pastikan Anda memiliki:

- Node.js 18+
- Docker dan Docker Compose
- Git

## 🚀 Instalasi dan Setup Lokal

### 1. Clone Repository
```bash
git clone <repository-url>
cd mybinder
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Setup Environment Variables
```bash
cp .env.example .env.local
```

Edit file `.env.local` dan sesuaikan dengan konfigurasi Anda:
```env
# Database
DATABASE_URL="postgresql://postgres:password@localhost:5432/mybinder"

# JWT Secret
JWT_SECRET="your-super-secret-jwt-key-here"

# Next.js
NEXTAUTH_SECRET="your-nextauth-secret-here"
NEXTAUTH_URL="http://localhost:3000"
```

### 4. Setup Database dengan Docker
```bash
# Jalankan PostgreSQL container
docker-compose up -d

# Generate Prisma client
npx prisma generate

# Jalankan migrasi database
npx prisma db push

# Seed database dengan data demo
npm run db:seed
```

### 5. Jalankan Development Server
```bash
npm run dev
```

Buka [http://localhost:3000](http://localhost:3000) di browser Anda.

## 🧪 Testing

Jalankan test suite lengkap:
```bash
# Jalankan semua test
npm test

# Jalankan test dengan coverage
npm run test:coverage

# Jalankan test dalam watch mode
npm run test:watch
```

## 👥 Akun Demo

Aplikasi dilengkapi dengan akun demo untuk evaluasi dan testing:

### 🔑 Demo Accounts
| Email | Password | Role | Group Access |
|-------|----------|------|--------------|
| `<EMAIL>` | `demo123` | Owner | Demo Team (Owner) |
| `<EMAIL>` | `demo123` | Member | Demo Team (Member) |

### 🌐 Live Application
- **Production URL**: [https://mybinder.vercel.app](https://mybinder.vercel.app)
- **Status**: ✅ Fully functional dan stable
- **Features**: Semua fitur tersedia untuk testing

### 🧪 Testing Scenarios
1. **Group Management**: Create, join, leave groups
2. **Role-based Access**: Test different permission levels
3. **Real-time Messaging**: Send messages dan replies
4. **Block-based Notes**: Create dan edit collaborative notes
5. **Mobile Experience**: Test responsive design dan touch gestures

## 📁 Struktur Project

```
mybinder/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes (RESTful endpoints)
│   │   │   ├── auth/          # Authentication endpoints
│   │   │   ├── groups/        # Group management API
│   │   │   ├── messages/      # Messaging API
│   │   │   ├── notes/         # Notes API
│   │   │   └── blocks/        # Block editor API
│   │   ├── dashboard/         # Dashboard pages
│   │   └── layout.tsx         # Root layout dengan providers
│   ├── components/            # React components (modular)
│   │   ├── auth/             # Authentication UI
│   │   ├── groups/           # Group management UI
│   │   ├── messages/         # Chat interface
│   │   ├── notes/            # Block-based editor
│   │   ├── threads/          # Threading system
│   │   └── common/           # Reusable UI components
│   ├── contexts/             # React Context providers
│   │   ├── AuthContext.tsx   # User authentication state
│   │   ├── GroupContext.tsx  # Group management state
│   │   ├── MessageContext.tsx # Real-time messaging state
│   │   └── NotesContext.tsx  # Notes collaboration state
│   ├── hooks/                # Custom React hooks
│   │   ├── useIsMobile.ts    # Mobile detection
│   │   ├── useSwipeGesture.ts # Touch gestures
│   │   └── useSmartPosition.ts # UI positioning
│   ├── lib/                  # Utility libraries
│   │   ├── auth.ts           # JWT utilities
│   │   ├── prisma.ts         # Database client
│   │   ├── middleware.ts     # API middleware
│   │   └── haptics.ts        # Mobile haptic feedback
│   ├── types/                # TypeScript type definitions
│   └── __tests__/            # Comprehensive test suite
├── prisma/                   # Database layer
│   ├── schema.prisma         # Database schema
│   ├── migrations/           # Database migrations
│   └── seed.ts              # Demo data seeding
├── tests/                    # E2E tests (Playwright)
├── docker-compose.yml        # Local development database
└── README.md                # Project documentation
```

## 🚀 Deployment ke Production

### Persiapan Deployment

1. **Setup Supabase Database**
   - Buat project baru di [Supabase](https://supabase.com)
   - Dapatkan connection string dari Settings > Database
   - Update `DATABASE_URL` di environment variables

2. **Setup Vercel**
   - Push code ke GitHub repository
   - Connect repository ke [Vercel](https://vercel.com)
   - Configure environment variables di Vercel dashboard

### Environment Variables untuk Production

```env
# Database (Supabase)
DATABASE_URL="postgresql://postgres:[password]@[host]:5432/postgres"

# JWT Secret (generate random string)
JWT_SECRET="your-production-jwt-secret"

# Next.js
NEXTAUTH_SECRET="your-production-nextauth-secret"
NEXTAUTH_URL="https://your-app.vercel.app"
```

### Deploy ke Vercel

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

## 📚 API Documentation

### Authentication Endpoints

#### POST `/api/auth/login`
Login pengguna dengan email dan password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "message": "Login successful",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "username": "username",
    "name": "User Name"
  },
  "token": "jwt-token"
}
```

#### POST `/api/auth/register`
Registrasi pengguna baru.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "password": "password123",
  "name": "User Name"
}
```

### Group Management Endpoints

#### GET `/api/groups`
Mendapatkan daftar grup pengguna.

#### POST `/api/groups`
Membuat grup baru.

**Request Body:**
```json
{
  "name": "Group Name",
  "description": "Group Description"
}
```

#### GET `/api/groups/[groupId]`
Mendapatkan detail grup dan pesan.

#### POST `/api/groups/[groupId]/messages`
Mengirim pesan ke grup.

**Request Body:**
```json
{
  "content": "Message content"
}
```

### Notes Endpoints

#### GET `/api/groups/[groupId]/notes`
Mendapatkan daftar catatan grup.

#### POST `/api/groups/[groupId]/notes`
Membuat catatan baru.

**Request Body:**
```json
{
  "title": "Note Title",
  "blocks": [
    {
      "type": "text",
      "content": "Note content"
    }
  ]
}
```

## 🔧 Development Commands

```bash
# Development
npm run dev              # Start development server dengan Turbopack
npm run build           # Build for production dengan optimizations
npm run start           # Start production server

# Database Management
npm run db:seed         # Seed database dengan demo data
npm run db:reset        # Reset database dan re-seed
npx prisma generate     # Generate Prisma client
npx prisma db push      # Push schema changes ke database
npx prisma studio       # Open Prisma Studio (database GUI)

# Testing Suite
npm test                # Run unit tests dengan Jest
npm run test:watch      # Run tests dalam watch mode
npm run test:coverage   # Run tests dengan coverage report
npx playwright test     # Run E2E tests dengan Playwright
npx playwright test --ui # Run E2E tests dengan UI mode

# Code Quality
npm run lint            # Run ESLint untuk code quality
npm run lint:fix        # Auto-fix ESLint issues
npx tsc --noEmit        # TypeScript type checking
```

## 🤝 Contributing

1. Fork repository
2. Buat feature branch (`git checkout -b feature/amazing-feature`)
3. Commit perubahan (`git commit -m 'Add amazing feature'`)
4. Push ke branch (`git push origin feature/amazing-feature`)
5. Buat Pull Request

## 📝 License

Project ini menggunakan MIT License. Lihat file `LICENSE` untuk detail lengkap.

## 🆘 Troubleshooting

### Database Connection Issues
```bash
# Restart Docker containers
docker-compose down
docker-compose up -d

# Reset database
npx prisma db push --force-reset
npm run db:seed
```

### Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Or use different port
npm run dev -- -p 3001
```

### Environment Variables Not Loading
- Pastikan file `.env.local` ada di root directory
- Restart development server setelah mengubah environment variables
- Periksa nama variable sesuai dengan yang digunakan di kode

## 📞 Support

Jika Anda mengalami masalah atau memiliki pertanyaan:

1. Periksa [Issues](https://github.com/mybinder/issues) yang sudah ada
2. Buat issue baru dengan detail lengkap
3. Sertakan log error dan langkah reproduksi

## 🏆 Project Achievements

### ✅ Requirements Completion
- **Core Features**: ✅ Group management, messaging, notes
- **Technical Stack**: ✅ Next.js, PostgreSQL, Tailwind CSS
- **Additional Features**: ✅ Role-based access, threading, mobile optimization
- **Production Deployment**: ✅ Live application dengan demo accounts
- **Documentation**: ✅ Comprehensive technical documentation

---

**Dibuat dengan ❤️ menggunakan Next.js, TypeScript, dan modern development practices**
